<?php

namespace App\Http\Controllers;

use App\Models\Hotel;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class HotelController extends Controller
{
    /**
     * Display a listing of hotels (Super Admin only).
     */
    public function index()
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized');
        }

        $hotels = Hotel::with('users')->paginate(10);
        return view('admin.hotels.index', compact('hotels'));
    }

    /**
     * Show the form for creating a new hotel.
     */
    public function create()
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized');
        }

        return view('admin.hotels.create');
    }

    /**
     * Store a newly created hotel.
     */
    public function store(Request $request)
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized');
        }

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:hotels,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'tin' => 'nullable|string|max:50',
            'description' => 'nullable|string',
            'website' => 'nullable|url',
            'admin_name' => 'required|string|max:255',
            'admin_email' => 'required|email|unique:users,email',
            'admin_password' => 'required|string|min:8',
        ]);

        // Create hotel
        $hotel = Hotel::create([
            'name' => $validatedData['name'],
            'slug' => Str::slug($validatedData['name']),
            'email' => $validatedData['email'],
            'phone' => $validatedData['phone'],
            'address' => $validatedData['address'],
            'tin' => $validatedData['tin'],
            'description' => $validatedData['description'],
            'website' => $validatedData['website'],
            'status' => 'active',
            'trial_ends_at' => now()->addDays(30), // 30-day trial
        ]);

        // Create admin user for the hotel
        $adminUser = User::create([
            'name' => $validatedData['admin_name'],
            'email' => $validatedData['admin_email'],
            'password' => bcrypt($validatedData['admin_password']),
            'user_type' => 'admin',
            'role' => 'admin',
        ]);

        // Attach user to hotel
        $hotel->users()->attach($adminUser->id, [
            'role' => 'admin',
            'is_active' => true,
        ]);

        // Create default room types for the hotel
        $defaultRoomTypes = [
            ['name' => 'Single', 'description' => 'Single Room', 'rate' => 1000.00],
            ['name' => 'Double', 'description' => 'Double Room', 'rate' => 1500.00],
            ['name' => 'Family', 'description' => 'Family Room', 'rate' => 2000.00],
            ['name' => 'Suite', 'description' => 'Suite Room', 'rate' => 2500.00],
        ];

        foreach ($defaultRoomTypes as $roomType) {
            $hotel->roomTypes()->create($roomType);
        }

        // Create default settings for the hotel
        $defaultSettings = [
            ['key' => 'site_name', 'value' => $hotel->name],
            ['key' => 'site_description', 'value' => $hotel->description ?? $hotel->name . ' - Hotel Management'],
            ['key' => 'contact_email', 'value' => $hotel->email],
            ['key' => 'phone', 'value' => $hotel->phone ?? ''],
            ['key' => 'address', 'value' => $hotel->address ?? ''],
            ['key' => 'tin', 'value' => $hotel->tin ?? ''],
            ['key' => 'paypal_service_fee', 'value' => '5'],
            ['key' => 'paymongo_service_fee', 'value' => '5'],
        ];

        foreach ($defaultSettings as $setting) {
            $hotel->settings()->create($setting);
        }

        log_activity('Created new hotel: ' . $hotel->name, 'Hotel Module');

        return redirect()->route('admin.hotels.index')->with('success', 'Hotel created successfully!');
    }

    /**
     * Display the specified hotel.
     */
    public function show(Hotel $hotel)
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized');
        }

        $hotel->load(['users', 'rooms', 'bookings']);
        return view('admin.hotels.show', compact('hotel'));
    }

    /**
     * Show the form for editing the specified hotel.
     */
    public function edit(Hotel $hotel)
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized');
        }

        return view('admin.hotels.edit', compact('hotel'));
    }

    /**
     * Update the specified hotel.
     */
    public function update(Request $request, Hotel $hotel)
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized');
        }

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:hotels,email,' . $hotel->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'tin' => 'nullable|string|max:50',
            'description' => 'nullable|string',
            'website' => 'nullable|url',
            'status' => 'required|in:active,inactive,suspended',
        ]);

        $hotel->update($validatedData);

        log_activity('Updated hotel: ' . $hotel->name, 'Hotel Module');

        return redirect()->route('admin.hotels.index')->with('success', 'Hotel updated successfully!');
    }

    /**
     * Switch to a different hotel (for super admin).
     */
    public function switch(Request $request)
    {
        $hotelId = $request->input('hotel_id');
        $user = Auth::user();

        if ($user->isSuperAdmin() || $user->hasAccessToHotel($hotelId)) {
            session(['current_hotel_id' => $hotelId]);
            return redirect()->back()->with('success', 'Switched hotel successfully!');
        }

        return redirect()->back()->with('error', 'You do not have access to this hotel.');
    }

    /**
     * Get current hotel information.
     */
    public function current()
    {
        $hotelId = session('current_hotel_id');
        if ($hotelId) {
            $hotel = Hotel::find($hotelId);
            return response()->json($hotel);
        }
        
        return response()->json(null);
    }
}
