<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\AdditionalCharge;
use Illuminate\Support\Facades\Log;
use App\Models\Order;//vince added
use App\Models\OrderItem;//vince added
use App\Models\Setting;//vince added

class InvoiceController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $invoices = Invoice::with('booking')->whereHas('booking')->latest()->get();//vince added ->whereHas('booking')
        $title = 'Booking Invoices';
        return view('admin.invoices.view-invoices', compact('invoices', 'title'));
    }
    //vince added function
    public function posinvoices()
    {
        $invoices = Invoice::with('order')
            ->whereDoesntHave('booking')
            ->whereHas('order') // Ensures there's an associated order
            ->latest()
            ->get();
        $title = 'POS Invoices';
        return view('admin.invoices.view-posinvoices', compact('invoices', 'title'));
    }
    //vince added function
    public function viewPosInv($id){
        $invoice = Invoice::find($id);
        $order = Order::where('invoice_id', $id)->first();
        if ($invoice && $order){
            return view('admin.invoices.view-posinv', compact('invoice', 'order'));
        } else {
            return redirect()->route('admin.posinvoices')->with('error', 'POS invoice is not found');
        } 
    }
    //vince added function
    public function printInvoice($id){
        $invoice = Invoice::find($id);
        $order = Order::where('invoice_id', $id)->first();
        $setting = Setting::all();
        if ($invoice && $order){
            $orderItems = OrderItem::where('order_id', $order->id)->with('menu')->get();
            return view('admin.invoices.print-posinv', compact('invoice', 'order', 'setting','orderItems'));
        } else {
            return redirect()->route('admin.posinvoices')->with('error', 'POS invoice is not found');
        } 
    }
    /**
     * Show the form for creating a new resource.
     */
    public function add($id)
    {
        //check if invoice already exists
        $existingInvoice = Invoice::where('booking_id', $id)->first();
        if ($existingInvoice) {
            return redirect()->route('admin.bookings')->with('error', 'Invoice already exists for this booking.');
        }
        
        $booking = Booking::findOrFail($id);
        return view('admin.invoices.add-invoices', compact('booking'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'additional_charge_description' => 'nullable|array',
            'additional_charge_description.*' => 'nullable|string',
            'additional_charge_amount' => 'nullable|array',
            'additional_charge_amount.*' => 'nullable|numeric',
            'total_amount' => 'required|numeric',
            'created_by' => 'nullable|string',
        ]);

        $booking = Booking::findOrFail($request->input('booking_id'));

        // Check if an invoice already exists for this booking
        $existingInvoice = Invoice::where('booking_id', $booking->id)->first();

        if ($existingInvoice) {
            return redirect()->route('admin.bookings')->with('error', 'Invoice already exists for this booking.');
        }

        // Create the invoice with total amount
        $invoice = Invoice::create([
            'booking_id' => $booking->id,
            'total_amount' => $request->input('total_amount'),
            'invoice_date' => now(),
            'created_by' => $request->input('created_by'),
        ]);

        $chargeDescriptions = $request->input('additional_charge_description', []);
        $chargeAmounts = $request->input('additional_charge_amount', []);


        // Store additional charges
        foreach ($chargeDescriptions as $index => $description) {
            $amount = $chargeAmounts[$index] ?? null;

            AdditionalCharge::create([
                'invoice_id' => $invoice->id,
                'amount' => $amount,
                'description' => $description,
            ]);
        }

        //Log activity
        log_activity('Invoice generated for booking ID: ' . $booking->id, 'Invoice Module');

        return redirect()->route('admin.bookings')->with('success', 'Invoice generated successfully!');
    }




    /**
     * Display the specified resource.
     */
    public function show(Invoice $invoice)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        //load invoice data for editing with relationship
        $invoice = Invoice::with('booking', 'additionalCharges')->findOrFail($id);
        return view('admin.invoices.edit-invoices', compact('invoice'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
{
    $request->validate([
        'additional_charge_description' => 'nullable|array',
        'additional_charge_description.*' => 'nullable|string',
        'additional_charge_amount' => 'nullable|array',
        'additional_charge_amount.*' => 'nullable|numeric|min:0',
        'total_amount' => 'required|numeric|min:0',
        'updated_by' => 'nullable|string',
    ]);

    // Find the invoice by ID
    $invoice = Invoice::with('additionalCharges')->findOrFail($id);

    // Update the invoice with the new total amount
    $invoice->update([
        'total_amount' => $request->input('total_amount'),
        'invoice_date' => now(),  // Adjust if you don't want to change the date
        'updated_by' => $request->input('updated_by'),
    ]);

    // Get additional charges from the request
    $chargeDescriptions = $request->input('additional_charge_description', []);
    $chargeAmounts = $request->input('additional_charge_amount', []);


    // Delete the old additional charges
    $invoice->additionalCharges()->delete();

    // Recreate the additional charges
    foreach ($chargeDescriptions as $index => $description) {
        $amount = $chargeAmounts[$index] ?? null;

        if ($amount !== null && $description !== null) {
            AdditionalCharge::create([
                'invoice_id' => $invoice->id,
                'amount' => $amount,
                'description' => $description,
            ]);
        }
    }
    //Log activity
    log_activity('Invoice updated for booking ID: ' . $invoice->booking->id, 'Invoice Module');
    
    return redirect()->route('admin.invoices')->with('success', 'Invoice updated successfully!');
}



    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Invoice $invoice)
    {
        //
    }
}
