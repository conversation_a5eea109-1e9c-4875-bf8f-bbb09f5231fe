<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Ixudra\Curl\Facades\Curl;
use Illuminate\Support\Facades\Session;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Menu;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\AdditionalCharge;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

use Illuminate\Support\Facades\Log;


class PayMongoController extends Controller
{
    public function payfood_order_success()
    {
        // Get the checkout session ID from the session
        $checkout_session_id = Session::get('checkout_session_id');

        // Get the payment details from PayMongo
        $response = Curl::to('https://api.paymongo.com/v1/checkout_sessions/' . $checkout_session_id)
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . base64_encode(env('PAYMONGO_SECRET_KEY') . ':'))
            ->asJson()
            ->get();

        if (isset($response->data)) {
            $checkoutData = $response->data->attributes;
            $payment = new Payment();
            $payment->payment_method = $checkoutData->payments[0]->attributes->source->type;
            $payment->payment_date = now();

            // Assuming we need to sum line item amounts
            $totalAmount = 0;
            foreach ($response->data->attributes->line_items as $item) {
                $totalAmount += $item->amount;
            }
            $payment->amount = $totalAmount / 100; // Convert centavos to pesos
            $payment->save();

            $order = new Order();
            $order->order_number = strtoupper(uniqid('ORD'));
            $order->customer_name = $response->data->attributes->customer_name ?? 'Restaurant Customer';
            $order->total_price = $payment->amount;
            $order->save();

            foreach ($response->data->attributes->line_items as $item) {
                $menuItem = Menu::where('name', $item->name)->first();

                if ($menuItem) {
                    $orderItem = new OrderItem();
                    $orderItem->order_id = $order->id;
                    $orderItem->menu_id = $menuItem->id;
                    $orderItem->quantity = $item->quantity;
                    $orderItem->price = $item->amount / 100;
                    $orderItem->save();
                } else {
                    // Handle the case where the menu item was not found
                    Log::error("Menu item not found for name: " . $item->name);
                    // Optionally, you can continue with the next item or handle this case differently
                    continue;
                }
            }


            Session::forget('checkout_session_id');

            return back()->with('success', 'Payment successful.');
        } else {
            return back()->withErrors(['msg' => 'Failed to retrieve payment details. Please try again.']);
        }
    }
    public function payfood_order(Request $request)
    {
        $data = json_decode($request->input('orderData'), true);

        // Check if JSON decoding was successful
        if (json_last_error() !== JSON_ERROR_NONE) {
            return back()->withErrors(['msg' => 'Invalid order data format.']);
        }

        // Fetch the service fee percentage
        $serviceFeePercentage = get_setting('paymongo_service_fee', 0) / 100;
        $serviceFeeInCentavos = $data['total_amount'] * $serviceFeePercentage * 100;
        $customerName = $data['customer_name'] ?? 'Restaurant Customer';

        // Prepare line items
        $lineItems = [
            [
                'currency'      => 'PHP',
                'amount'        => round($serviceFeeInCentavos),
                'description'   => 'Hotel Paymongo Service Fee',
                'name'          => 'Hotel Paymongo Service Fee - ' . get_setting('paymongo_service_fee', 0) . '%',
                'quantity'      => 1,
            ],
        ];

        foreach ($data['items'] as $order_item) {
            $menu_item = Menu::find($order_item['menu_id']);
            if ($menu_item) {
                $lineItems[] = [
                    'currency'      => 'PHP',
                    'amount'        => $menu_item->price * 100,
                    'description'   => $menu_item->description,
                    'name'          => $menu_item->name,
                    'quantity'      => $order_item['quantity'],
                ];
            }
        }

        $payload = [
            'data' => [
                'attributes' => [
                    'line_items' => $lineItems,
                    'payment_method_types' => ['card', 'paymaya', 'grab_pay', 'gcash','qrph'],
                    'success_url' => route('admin.payments.paymongo.get.payfood_order_success'),
                    'cancel_url' => route('admin.payments.paymongo.get.cancel'),
                    'description' => 'FOOD ORDER Payment for ' . $customerName,
                    'customer_name' => $customerName,
                ]
            ]
        ];

        $response = Curl::to('https://api.paymongo.com/v1/checkout_sessions')
            ->withHeader('Content-Type: application/json')
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . base64_encode(env('PAYMONGO_SECRET_KEY') . ':'))
            ->withData($payload)
            ->asJson()
            ->post();

        Log::info('PayMongo response: ', (array) $response);

        if (isset($response->data)) {
            Session::put('checkout_session_id', $response->data->id);
            $checkoutUrl = $response->data->attributes->checkout_url;
    
            // Generate QR Code
            $qrCodeSvg = QrCode::size(200)->generate($checkoutUrl);
    
            // Convert QR code SVG to base64
            $qrCodeBase64 = base64_encode($qrCodeSvg);
    
            return response()->json([
                'redirect_url' => $checkoutUrl,
                'qr_code' => $qrCodeBase64
            ]);
        } else {
            return back()->withErrors(['msg' => 'Failed to create checkout session. Please try again.']);
        }
    }





    public function pay($invoice_id)
    { //clear the checkout session id
        Session::forget('checkout_session_id');

        // Check if payment is already made
        $payment = Payment::where('invoice_id', $invoice_id)->first();
        if ($payment) {
            return redirect()->route('admin.invoices')->with('error', 'Payment already made for this invoice.');
        }

        // Get invoice details with relationships
        $invoice = Invoice::with('booking', 'additionalCharges')->findOrFail($invoice_id);

        // Fetch the service fee percentage
        $serviceFeePercentage = get_setting('paymongo_service_fee', 0) / 100;
        $serviceFeeInCentavos = $invoice->total_amount * $serviceFeePercentage * 100;

        // Prepare line items
        $lineItems = [
            [
                'currency'      => 'PHP',
                'amount'        => $invoice->booking->total_price * 100,
                'description'   => 'Booking Payment for Room ' . $invoice->booking->room->room_number,
                'name'          => 'Room Type: ' . $invoice->booking->room->roomType->name,
                'quantity'      => 1,
            ],
            [
                'currency'      => 'PHP',
                'amount'        => round($serviceFeeInCentavos),
                'description'   => 'Hotel Paymongo Service Fee ',
                'name'          => 'Hotel Paymongo Service Fee - ' . get_setting('paymongo_service_fee', 0) . '%',
                'quantity'      => 1,
            ],
        ];

        foreach ($invoice->additionalCharges as $charge) {
            $lineItems[] = [
                'currency'      => 'PHP',
                'amount'        => $charge->amount * 100,
                'description'   => $charge->description,
                'name'          => 'Additional Charge',
                'quantity'      => 1,
            ];
        }

        $data = [
            'data' => [
                'attributes' => [
                    'line_items' => $lineItems,
                    'payment_method_types' => ['card', 'paymaya', 'grab_pay', 'gcash'],
                    'success_url' => 'http://localhost:8000/admin/payments/paymongo/success/' . $invoice->id,
                    'cancel_url' => 'http://localhost:8000/admin/payments/paymongo/cancel',
                    'description' => 'Payment for Invoice #' . $invoice->id . ' Guest: ' . $invoice->booking->guest_first_name . ' ' . $invoice->booking->guest_last_name,
                ]
            ]
        ];

        $response = Curl::to('https://api.paymongo.com/v1/checkout_sessions')
            ->withHeader('Content-Type: application/json')
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . base64_encode(env('PAYMONGO_SECRET_KEY') . ':'))
            ->withData($data)
            ->asJson()
            ->post();

        Log::info('PayMongo response: ', (array) $response);
        if (isset($response->data)) {
            Session::forget('checkout_session_id');
            // Save the checkout session ID to the session
            Session::put('checkout_session_id', $response->data->id);

            // Redirect to PayMongo checkout URL
            return redirect()->to($response->data->attributes->checkout_url);
        } else {
            return back()->withErrors(['msg' => 'Failed to create checkout session. Please try again.']);
        }
    }



    public function success($invoice_id)
    {
        Log::info('Invoice ID: ' . $invoice_id);
        Log::info('Checkout Session ID: ' . Session::get('checkout_session_id'));
        $checkout_session_id = Session::get('checkout_session_id');

        // Get the payment details from PayMongo
        $response = Curl::to('https://api.paymongo.com/v1/checkout_sessions/' . $checkout_session_id)
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . base64_encode(env('PAYMONGO_SECRET_KEY') . ':'))
            ->asJson()
            ->get();

        Log::info('PayMongo checkout details response: ', (array) $response);

        if (isset($response->data)) {
            $checkoutData = $response->data->attributes;

            // Calculate total amount from all line items
            $totalAmount = 0;
            foreach ($checkoutData->line_items as $lineItem) {
                $totalAmount += $lineItem->amount; // In centavos
            }
            $totalAmountInPesos = $totalAmount / 100; // Convert to pesos

            Log::info('Total Amount: ' . $totalAmountInPesos);

            // Check if the payment method type is available in the response
            if (isset($checkoutData->payments) && count($checkoutData->payments) > 0) {
                // Assuming the first payment contains the payment method
                $paymentMethod = $checkoutData->payments[0]->attributes->source->type;
            } else {
                // Fallback if payment method is not found
                $paymentMethod = 'unknown';
            }

            Log::info('Payment Method: ' . $paymentMethod);

            // Save payment details
            $payment = new Payment();
            $payment->invoice_id = $invoice_id;
            $payment->amount = $totalAmountInPesos; // Save the total amount
            $payment->payment_method = $paymentMethod;
            $payment->payment_date = now();
            $payment->save();

            // Update invoice status to paid
            $invoice = Invoice::findOrFail($invoice_id);
            $invoice->status = 'paid';
            $invoice->save();

            // Log the activity
            log_activity('PayMongo Payment made for Invoice #' . $invoice_id, 'Payment Module');

            return redirect()->route('admin.invoices')->with('success', 'Payment successful.');
        } else {
            return redirect()->route('admin.invoices')->with('error', 'Failed to retrieve payment details.');
        }
    }







    public function cancel()
    {
        return redirect()->route('admin.invoices')->with('error', 'Payment cancelled.');
    }



    public function linkPay()
    {
        $data['data']['attributes']['amount'] = 150050;
        $data['data']['attributes']['description'] = 'Test transaction.';

        $response = Curl::to('https://api.paymongo.com/v1/links')
            ->withHeader('Content-Type: application/json')
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . env('AUTH_PAY'))
            ->withData($data)
            ->asJson()
            ->post();

        dd($response);
    }

    public function linkStatus($linkid)
    {
        $response = Curl::to('https://api.paymongo.com/v1/links/' . $linkid)
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . env('AUTH_PAY'))
            ->asJson()
            ->get();

        dd($response);
    }


    public function refund()
    {

        $data['data']['attributes']['amount']       = 5000;
        $data['data']['attributes']['payment_id']   = 'pay_sA83KrtmJUdue8prEHD6rZrY';
        $data['data']['attributes']['reason']       = 'duplicate';

        $response = Curl::to('https://api.paymongo.com/refunds')
            ->withHeader('Content-Type: application/json')
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . env('AUTH_PAY'))
            ->withData($data)
            ->asJson()
            ->post();

        dd($response);
    }

    public function refundStatus($id)
    {
        $response = Curl::to('https://api.paymongo.com/refunds/' . $id)
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . env('AUTH_PAY'))
            ->asJson()
            ->get();

        dd($response);
    }
}
