<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use App\Models\Invoice;
use GuzzleHttp\Psr7\Request;
use Illuminate\Http\Request as Requests;

use App\Models\Payment;

class PayPalController extends Controller
{
    /**
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function index($invoice_id)
    {
        //Check if payment is already done
        $payment = Payment::where('invoice_id', $invoice_id)->first();

        if ($payment) {
            return redirect()->route('admin.invoices')->with('error', 'Payment already done for this invoice');
        } else {
            //Return Checkout Page with Invoice Details with additionalCharges relationship
            $invoice = Invoice::with('additionalCharges')->find($invoice_id);
            return view('admin.payments.paypal-checkout', compact('invoice'));
        }
    }

    /**
     * @return string
     */
    private function getAccessToken(): string
    {
        $headers = [
            'Content-Type'  => 'application/x-www-form-urlencoded',
            'Authorization' => 'Basic ' . base64_encode(config('paypal.client_id') . ':' . config('paypal.client_secret'))
        ];

        $response = Http::withHeaders($headers)
            ->withBody('grant_type=client_credentials')
            ->post(config('paypal.base_url') . '/v1/oauth2/token');

        return json_decode($response->body())->access_token;
    }

    /**
     * @return string
     */
    public function create(int $amount = 10): string
    {
        $id = uuid_create();

        $headers = [
            'Content-Type'      => 'application/json',
            'Authorization'     => 'Bearer ' . $this->getAccessToken(),
            'PayPal-Request-Id' => $id,
        ];

        $body = [
            "intent"         => "CAPTURE",
            "purchase_units" => [
                [
                    "reference_id" => $id,
                    "amount"       => [
                        "currency_code" => config('paypal.currency'),
                        "value" => number_format($amount, 2, '.', ''),
                    ]
                ]
            ]
        ];

        $response = Http::withHeaders($headers)
            ->withBody(json_encode($body))
            ->post(config('paypal.base_url') . '/v2/checkout/orders');

        Session::put('request_id', $id);
        Session::put('order_id', json_decode($response->body())->id);

        return json_decode($response->body())->id;
    }

    /**
     * @return mixed
     */
    public function complete(Requests $request)
    {
        //Extract invoice_id
        $invoiceId = $request->input('invoice_id');

        //Check if payment is already done
        $payment = Payment::where('invoice_id', $invoiceId)->first();

      

            //Capture Payment
            $url = config('paypal.base_url') . '/v2/checkout/orders/' . Session::get('order_id') . '/capture';

            $headers = [
                'Content-Type'  => 'application/json',
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
            ];


            $response = Http::withHeaders($headers)
                ->post($url, null);

            //Save Payment in Database
            $payment = new Payment();
            $payment->invoice_id = $invoiceId;
            $payment->amount = json_decode($response->body())->purchase_units[0]->payments->captures[0]->amount->value;
            $payment->payment_method = 'PayPal';
            $payment->payment_date = now();
            $payment->save();

            //Update Invoice Status to Paid
            $invoice = Invoice::find($invoiceId);
            $invoice->status = 'paid';
            $invoice->save();

            //Log the activity
            log_activity('PayPal Payment added', 'Payment Module');

            return json_decode($response->body());
            //return view('admin.payments.checkout')->with('success', 'Payment Successful');
        
    }
}
