<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class QrCodeController extends Controller
{
    public function generateQrCode($type, $id)
    {
        $url = $this->getPaymentUrl($type, $id);
        return QrCode::size(400)->generate($url);
    }

    private function getPaymentUrl($type, $id)
    {
        switch ($type) {
            case 'paypal':
                return route('admin.payments.paypal', ['invoice_id' => $id]);
            case 'paymongo':
                return route('admin.payments.paymongo', ['invoice_id' => $id]);
            default:
                return '#';
        }
    }
}
