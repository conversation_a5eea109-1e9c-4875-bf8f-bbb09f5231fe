<?php

use App\Models\Setting;
use Illuminate\Support\Facades\Auth;
use App\Models\ActivityLog;

if (!function_exists('get_setting')) {
    function get_setting($key, $default = null)
    {
        $hotelId = session('current_hotel_id');
        if ($hotelId) {
            return Setting::where('hotel_id', $hotelId)->where('key', $key)->value('value') ?? $default;
        }
        return $default;
    }
}

if (!function_exists('get_current_hotel')) {
    function get_current_hotel()
    {
        $hotelId = session('current_hotel_id');
        if ($hotelId) {
            return \App\Models\Hotel::find($hotelId);
        }
        return null;
    }
}

if (!function_exists('get_user_email')) {
    function get_user_email()
    {
        return Auth::check() ? Auth::user()->email : 'no user';
    }
}

if (!function_exists('log_activity')) {
    function log_activity($description, $module = null)
    {
        $user_email = get_user_email();
        $hotelId = session('current_hotel_id');

        // Save the activity log to the database
        ActivityLog::create([
            'description' => $description,
            'user_email'  => $user_email,
            'module'      => $module,
            'hotel_id'    => $hotelId,
        ]);
    }
}

//Get the current user's role
if (!function_exists('get_user_role')) {
    function get_user_role()
    {
        return Auth::check() ? Auth::user()->role : 'no user';
    }
}
