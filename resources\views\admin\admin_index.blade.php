@include('layout.admin-header')
{{-- @include('layout.scripts') --}}
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">


    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <script src="https://code.highcharts.com/highcharts.js"></script>

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h1 class="h3 mb-0">Dashboard</h1>
                            @if(isset($currentHotel))
                                <p class="text-muted mb-0">
                                    <i class="fas fa-building"></i> {{ $currentHotel->name }}
                                    @if($currentHotel->isOnTrial())
                                        <span class="badge badge-warning ml-2">
                                            <i class="fas fa-clock"></i> Trial ({{ $currentHotel->trial_ends_at->diffForHumans() }})
                                        </span>
                                    @endif
                                </p>
                            @endif
                        </div>
                        <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm"><i
                                class="fas fa-download fa-sm text-white-50"></i> Generate Report</a>
                    </div>

                    <!-- Content Row -->
                    <div class="row">

                        <!-- Earnings (Monthly) Card Example -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Earnings (This Month)</div>
                                            <div class="h5 mb-0 font-weight-bold ">
                                                {{ $currencySymbol . number_format($monthlyEarnings, 2) }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Earnings (Annual) Card Example -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Earnings (This Year)</div>
                                            <div class="h5 mb-0 font-weight-bold ">
                                                {{ $currencySymbol . number_format($annualEarnings, 2) }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Expenses (Monthly) Card Example -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Expenses (Monthly)</div>
                                            <div class="h5 mb-0 font-weight-bold ">
                                                {{ $currencySymbol . number_format($monthlyExpenses, 2) }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Expenses (Annual) Card Example -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                Expenses (Annual)</div>
                                            <div class="h5 mb-0 font-weight-bold ">
                                                {{ $currencySymbol . number_format($annualExpenses, 2) }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        
                    </div>

                    <!-- Content Row -->

                    <div class="row">

                   <!-- Area Chart -->
                    <div class="col-xl-8 col-lg-7">
                        <div class="card shadow mb-4">
                            <!-- Card Header - Dropdown -->
                            <div class="card-header py-3 d-flex flex-column flex-md-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">Earnings Overview</h6>
                                <div class="btn-group mt-3 mt-md-0" role="group" aria-label="Basic example">
                                    <button type="button" class="btn btn-primary mb-2 mb-md-0" onclick="updateChart('week')">This Week</button>
                                    <button type="button" class="btn btn-primary mb-2 mb-md-0" onclick="updateChart('month')">This Month</button>
                                    <button type="button" class="btn btn-primary mb-2 mb-md-0" onclick="updateChart('year')">This Year</button>
                                    <button type="button" class="btn btn-primary mb-2 mb-md-0" onclick="updateChart('custom')">Custom Range</button>
                                </div>
                            </div>

                            <!-- Date Range Picker for Custom Range -->
                            <div id="customDateRange" class="col-md-3 mt-3" style="display: none;">
                                <input type="text" id="startDate" placeholder="Start Date" class="form-control mb-2" autocomplete="off">
                                <input type="text" id="endDate" placeholder="End Date" class="form-control mb-2" autocomplete="off">
                                <button onclick="applyCustomDateRange()" class="btn btn-primary">Apply</button>
                            </div>

                            <!-- Card Body -->
                            <div class="card-body">
                                <div id="totalPayments" class="mb-3"></div>
                                <div id="totalExpenses" class="mb-3"></div>
                                <div id="chartContainer"></div>
                            </div>
                        </div>
                    </div>



                        <!-- Pie Chart -->
                        <div class="col-xl-4 col-lg-5">
                            <div class="card shadow mb-4">
                                <!-- Card Header - Dropdown -->
                                <div
                                    class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">Revenue Sources</h6>
                                    <div class="dropdown no-arrow">
                                        <a class="dropdown-toggle" href="#" role="button"
                                            id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true"
                                            aria-expanded="false">
                                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                                            aria-labelledby="dropdownMenuLink">
                                            <div class="dropdown-header">Dropdown Header:</div>
                                            <a class="dropdown-item" href="#">Action</a>
                                            <a class="dropdown-item" href="#">Another action</a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item" href="#">Something else here</a>
                                        </div>
                                    </div>
                                </div>
                                <!-- Card Body -->
                                <div class="card-body">
                                        <div id="myPieChart"></div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    {{-- <div id="chartContainer" style="width: 100%; height: 400px;"></div> --}}

                    <!-- Content Row -->
                    <div class="row">

                        <!-- Content Column -->
                        <div class="col-lg-6 mb-4">

                            <!-- Remittances Card Example -->
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Remittances</h6>
                                </div>
                                <div class="card-body">
                                    <h4 class="small font-weight-bold">Unremitted Payments<span class="float-right">{{$unremittedPercentage}}%</span>
                                    </h4>
                                    <div class="progress mb-4">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: {{$unremittedPercentage}}%"
                                            aria-valuenow="{{$unremittedPercentage}}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    
                                    <h4 class="small font-weight-bold">Pending Remittances<span
                                            class="float-right">{{$unconfirmedPercentage}}%</span></h4>
                                    <div class="progress mb-4">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{$unconfirmedPercentage}}%"
                                            aria-valuenow="{{$unconfirmedPercentage}}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    

                                    <h4 class="small font-weight-bold">Remittances Deposited to Bank<span
                                            class="float-right">{{$depositedToBankPercentage}}%</span></h4>
                                    <div class="progress mb-4">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{$depositedToBankPercentage}}%"
                                            aria-valuenow="{{$depositedToBankPercentage}}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>

                                   
                                  
                                </div>
                            </div>

                            <!-- Color System -->
                            <div class="row">
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-primary text-white shadow">
                                        <div class="card-body">
                                            Primary
                                            <div class="text-white-50 small">#4e73df</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-success text-white shadow">
                                        <div class="card-body">
                                            Success
                                            <div class="text-white-50 small">#1cc88a</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-info text-white shadow">
                                        <div class="card-body">
                                            Info
                                            <div class="text-white-50 small">#36b9cc</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-warning text-white shadow">
                                        <div class="card-body">
                                            Warning
                                            <div class="text-white-50 small">#f6c23e</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-danger text-white shadow">
                                        <div class="card-body">
                                            Danger
                                            <div class="text-white-50 small">#e74a3b</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-secondary text-white shadow">
                                        <div class="card-body">
                                            Secondary
                                            <div class="text-white-50 small">#858796</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-light text-black shadow">
                                        <div class="card-body">
                                            Light
                                            <div class="text-black-50 small">#f8f9fc</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-4">
                                    <div class="card bg-dark text-white shadow">
                                        <div class="card-body">
                                            Dark
                                            <div class="text-white-50 small">#5a5c69</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="col-lg-6 mb-4">

                            <!-- Illustrations -->
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Illustrations</h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center">
                                        <img class="img-fluid px-3 px-sm-4 mt-3 mb-4" style="width: 25rem;"
                                            src="img/undraw_posting_photo.svg" alt="...">
                                    </div>
                                    <p>Add some quality, svg illustrations to your project courtesy of <a
                                            target="_blank" rel="nofollow" href="https://undraw.co/">unDraw</a>, a
                                        constantly updated collection of beautiful svg images that you can use
                                        completely free and without attribution!</p>
                                    <a target="_blank" rel="nofollow" href="https://undraw.co/">Browse Illustrations
                                        on
                                        unDraw &rarr;</a>
                                </div>
                            </div>

                            <!-- Approach -->
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Development Approach</h6>
                                </div>
                                <div class="card-body">
                                    <p>SB Admin 2 makes extensive use of Bootstrap 4 utility classes in order to reduce
                                        CSS bloat and poor page performance. Custom CSS classes are used to create
                                        custom components and custom utility classes.</p>
                                    <p class="mb-0">Before working with this theme, you should become familiar with
                                        the
                                        Bootstrap framework, especially the utility classes.</p>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>

    <script src="{{ asset('assets/js/jquery-ui.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('assets/css/jquery-ui.css') }}">

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>
    <link href="{{ asset('assets/css/daterangepicker.min.css') }}" rel="stylesheet" />
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>

    <!-- Page level plugins -->
    {{-- <script src="{{ asset('assets/js/Chart.min.js') }}"></script> --}}

    <!-- Page level custom scripts -->
    {{-- <script src="{{ asset('assets/js/chart-area-demo.js') }}"></script>
    <script src="{{ asset('assets/js/chart-pie-demo.js') }}"></script> --}}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
         window.myAreaChart = Highcharts.chart('chartContainer', {
                chart: {
                    type: 'area'
                },
                title: {
                    text: 'Payments and Expenses Overview'
                },
                xAxis: {
                    categories: @json($dates)
                },
                yAxis: {
                    title: {
                        text: 'Total Amount'
                    },
                    labels: {
                        formatter: function() {
                            return this.value + '{{ $currencySymbol }}'; // Modify as needed
                        }
                    }
                },
                tooltip: {
                    shared: true
                },
                series: [{
                    name: 'Payments',
                    data: @json($paymentData),
                    color: 'green',
                    fillColor: 'rgba(0, 255, 0, 0.2)' // Light green fill color
                }, {
                    name: 'Expenses',
                    data: @json($expenseData),
                    color: 'red',
                    fillColor: 'rgba(255, 0, 0, 0.05)' // Light red fill color
                }]
            });

        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (!token) {
                console.error('CSRF token not found');
                return;
            }

            // Continue with your logic
        });

        function getDateRange(startDate, endDate) {
            // Your logic to handle date range
            console.log('Start Date:', startDate);
            console.log('End Date:', endDate);
            // Return or process the date range as needed
        }

        function applyCustomDateRange() {
            let startDate = document.getElementById('startDate').value;
            let endDate = document.getElementById('endDate').value;

            // Ensure both dates are filled out
            if (startDate && endDate) {
                getDateRange(startDate, endDate);
            } else {
                alert('Please select both start and end dates.');
            }
        }

        function updateChart(filter) {
            let url = '/admin/fetch-chart-data';
            let data = {};

            if (filter === 'custom') {
                document.getElementById('customDateRange').style.display = 'block';
                return;
            } else if (typeof filter === 'object') {
                document.getElementById('customDateRange').style.display = 'none';
                data.startDate = filter.startDate;
                data.endDate = filter.endDate;
            } else {
                document.getElementById('customDateRange').style.display = 'none';
                data.filter = filter;
            }

            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': token
                    },
                    body: JSON.stringify(data),
                })
                .then(response => response.json())
                .then(data => {
                    // Display total payments above the chart
                    document.getElementById('totalPayments').innerText = `Total Payments: {{ $currencySymbol }}${data.totalPayments}`;
                    document.getElementById('totalExpenses').innerText = `Total Expenses: {{ $currencySymbol }}${data.totalExpenses}`;

                    window.myAreaChart = Highcharts.chart('chartContainer', {
                        chart: {
                            type: 'area'
                        },
                        title: {
                            text: 'Payments and Expenses Overview'
                        },
                        xAxis: {
                            categories: data.dates
                        },
                        yAxis: {
                            title: {
                                text: 'Total Amount'
                            },
                            labels: {
                                formatter: function() {
                                    return this.value + ' {{ $currencySymbol }}'; // Modify as needed
                                }
                            }
                        },
                        series: [{
                                name: 'Payments',
                                data: data.paymentData,
                                color: 'green',
                                fillColor: 'rgba(0, 255, 0, 0.2)'
                            },
                            {
                                name: 'Expenses',
                                data: data.expenseData,
                                color: 'red',
                                fillColor: 'rgba(255, 0, 0, 0.05)'
                            }
                        ]
                    });
                })
                .catch(error => console.error('Error:', error));
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initially load the chart with the default filter (e.g., 'week')
            updateChart('year');
        });



        function applyCustomDateRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (startDate && endDate) {
                updateChart({
                    startDate,
                    endDate
                });
            }
        }


        document.addEventListener('DOMContentLoaded', function() {
            // Initialize date pickers for the custom date range inputs
            // Assuming you use a datepicker library, such as jQuery UI or Bootstrap Datepicker
            $('#startDate').datepicker({
                dateFormat: 'yy-mm-dd'
            });
            $('#endDate').datepicker({
                dateFormat: 'yy-mm-dd'
            });
        });
    </script>

{{-- Pie chart script --}}
    <script>
document.addEventListener('DOMContentLoaded', function () {
    window.myPieChart = Highcharts.chart('myPieChart', {
        chart: {
            type: 'pie'
        },
        title: {
            text: 'Revenue Sources'
        },
        tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: 'pointer',
                dataLabels: {
                    enabled: true,
                    format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                }
            }
        },
        series: [{
            name: 'Revenue',
            colorByPoint: true,
            data: [
                { name: 'Cash', y: {{ $revenueSources['cash'] ?? 0 }}, color: 'rgba(0, 255, 0, 0.5)', fillColor: 'rgba(0, 255, 0, 0.8)' }, // Light green color with darker fill color
                { name: 'PayPal', y: {{ $revenueSources['paypal'] ?? 0 }}, color: 'rgba(255, 0, 0, 0.5)', fillColor: 'rgba(255, 0, 0, 0.8)' }, // Light red color with darker fill color
                { name: 'GCash', y: {{ $revenueSources['gcash'] ?? 0 }}, color: 'rgba(0, 0, 255, 0.5)', fillColor: 'rgba(0, 0, 255, 0.8)' }, // Light blue color with darker fill color
                { name: 'PayMaya', y: {{ $revenueSources['paymaya'] ?? 0 }}, color: 'rgba(255, 255, 0, 0.5)', fillColor: 'rgba(255, 255, 0, 0.8)' }, // Light yellow color with darker fill color
                { name: 'Card', y: {{ $revenueSources['card'] ?? 0 }}, color: 'rgba(255, 0, 255, 0.5)', fillColor: 'rgba(255, 0, 255, 0.8)' }, // Light purple color with darker fill color
                { name: 'GrabPay', y: {{ $revenueSources['grab_pay'] ?? 0 }}, color: 'rgba(0, 255, 255, 0.5)', fillColor: 'rgba(0, 255, 255, 0.8)' } // Light cyan color with darker fill color
              
            ]
        }]
    });
});

//function to rerender the pie chart for theme changes
function refreshcharts() {
    window.myAreaChart.destroy();
    window.myPieChart.destroy();
    window.myPieChart = Highcharts.chart('myPieChart', {
        chart: {
            type: 'pie'
        },
        title: {
            text: 'Revenue Sources'
        },
        tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: 'pointer',
                dataLabels: {
                    enabled: true,
                    format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                }
            }
        },
        series: [{
            name: 'Revenue',
            colorByPoint: true,
            data: [
                { name: 'Cash', y: {{ $revenueSources['cash'] ?? 0 }}, color: 'rgba(0, 255, 0, 0.5)', fillColor: 'rgba(0, 255, 0, 0.8)' }, // Light green color with darker fill color
                { name: 'PayPal', y: {{ $revenueSources['paypal'] ?? 0 }}, color: 'rgba(255, 0, 0, 0.5)', fillColor: 'rgba(255, 0, 0, 0.8)' }, // Light red color with darker fill color
                { name: 'GCash', y: {{ $revenueSources['gcash'] ?? 0 }}, color: 'rgba(0, 0, 255, 0.5)', fillColor: 'rgba(0, 0, 255, 0.8)' }, // Light blue color with darker fill color
                { name: 'PayMaya', y: {{ $revenueSources['paymaya'] ?? 0 }}, color: 'rgba(255, 255, 0, 0.5)', fillColor: 'rgba(255, 255, 0, 0.8)' }, // Light yellow color with darker fill color
                { name: 'Card', y: {{ $revenueSources['card'] ?? 0 }}, color: 'rgba(255, 0, 255, 0.5)', fillColor: 'rgba(255, 0, 255, 0.8)' }, // Light purple color with darker fill color
                { name: 'GrabPay', y: {{ $revenueSources['grab_pay'] ?? 0 }}, color: 'rgba(0, 255, 255, 0.5)', fillColor: 'rgba(0, 255, 255, 0.8)' } // Light cyan color with darker fill color
              
            ]
        }]
    });
}

    </script>

    



</body>

</html>
