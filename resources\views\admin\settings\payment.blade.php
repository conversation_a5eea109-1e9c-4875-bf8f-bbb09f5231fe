@include('layout.admin-header')
{{-- @include('layout.scripts') --}}

<body id="page-top">


    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')



                    <!-- Content Row -->
                    <div class="row">
                        <div class="container-fluid">
                            <h1 class="h3 mb-4 text-gray-800">Payment Settings</h1>
                            <!-- Form for payment settings -->
                            <form action="{{ route('admin.settings.update') }}" method="POST">
                                @csrf

                                <div class="row">

                                    <div class="form-group col-md-3">
                                        <label for="paypal_service_fee">Paypal Service Fee Percentage</label>
                                        <input autocomplete="off" type="text" name="paypal_service_fee"
                                            id="paypal_service_fee" class="form-control"
                                            value="{{ old('paypal_service_fee', get_setting('paypal_service_fee')) }}">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="paymongo_service_fee">Paymongo Service Fee Percentage</label>
                                        <input autocomplete="off" type="text" name="paymongo_service_fee"
                                            id="paymongo_service_fee" class="form-control"
                                            value="{{ old('paymongo_service_fee', get_setting('paymongo_service_fee')) }}">
                                    </div>

                                    

                                </div>

                                <button type="submit" class="btn btn-primary">Save Settings</button>
                            </form>

                        </div>
                    </div>


                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>

    <!-- Page level plugins -->
    <script src="{{ asset('assets/js/Chart.min.js') }}"></script>

    <!-- Page level custom scripts -->
    <script src="{{ asset('assets/js/chart-area-demo.js') }}"></script>
    <script src="{{ asset('assets/js/chart-pie-demo.js') }}"></script>

</body>

</html>
